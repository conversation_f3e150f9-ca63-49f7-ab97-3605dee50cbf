// 钉钉认证管理器
// 处理钉钉平台的用户认证和状态管理

class DingTalkAuthManager {
  constructor() {
    this.isAuthenticated = false;
    this.userInfo = null;
    this.organizations = [];
    this.selectedCorpId = null;
    this.environment = this.detectEnvironment();
    this.authListeners = new Set();
    this.cookieListenerSetup = false; // 标记Cookie监听器是否已设置

    // 存储键定义（与文档保持一致）
    this.STORAGE_KEYS = {
      LOGIN_KEY: "Login",
      MY_ORGS_KEY: "MY_ORGS_KEY",
      SELECTED_CORP_ID: "selectedCorpId",
      USER_SETTINGS: "userSettings",
      AUTH_TIMESTAMP: "dingtalk_auth_timestamp"
    };

    // 初始化安全管理器
    this.securityManager = typeof DingTalkSecurityManager !== 'undefined' ?
      new DingTalkSecurityManager() : null;

    // 立即设置Cookie监听器（优先级最高）
    this.setupCookieListener();

    // 初始化
    this.initializeAuth();

    // 设置定期检查机制（作为Cookie监听器的备用）
    this.setupPeriodicCheck();

    // 设置增强的状态同步机制
    this.setupEnhancedStateSync();
  }

  // 检测当前环境
  detectEnvironment() {
    // 在Chrome扩展中，我们通过构建时的环境变量来判断
    // 这里先默认为生产环境，后续可以通过配置文件或构建参数调整
    return 'production';
  }

  // 获取API基础URL
  getApiBaseUrl() {
    const domains = {
      production: "https://docs.dingtalk.com",
      pre: "https://pre-docs.dingtalk.com"
    };
    return domains[this.environment];
  }

  // 获取钉钉文档登录URL
  getAuthUrl() {
    return `${this.getApiBaseUrl()}/i`;
  }

  // 初始化认证状态
  async initializeAuth() {
    const traceId = `init_${Date.now()}`;

    try {
      console.log(`[${traceId}] 开始初始化钉钉认证管理器`);

      // 从存储中恢复认证状态
      await this.loadAuthState();

      // 验证Cookie状态
      const cookieValid = await this.validateAuthCookie();

      if (this.isAuthenticated && !cookieValid) {
        // 本地状态显示已认证但Cookie无效，清理状态
        console.warn(`[${traceId}] 检测到认证状态不一致，清理本地状态`);
        await this.clearAuthState();
      } else if (!this.isAuthenticated && cookieValid) {
        // Cookie有效但本地状态未认证，尝试获取用户信息
        console.log(`[${traceId}] 检测到有效Cookie，尝试恢复认证状态`);
        await this.fetchUserInfoAndSync();
      }

      // 设置Cookie监听器
      this.setupCookieListener();

      // 定期清理过期数据
      this.setupCleanupTimer();

      console.log(`[${traceId}] 钉钉认证管理器初始化完成`, {
        isAuthenticated: this.isAuthenticated,
        environment: this.environment,
        userInfo: this.userInfo ? this.userInfo.name : null,
        organizationCount: this.organizations.length
      });

    } catch (error) {
      console.error(`[${traceId}] 钉钉认证管理器初始化失败:`, error);
      await this.clearAuthState();
    }
  }

  // 设置清理定时器
  setupCleanupTimer() {
    // 每小时清理一次过期数据
    setInterval(() => {
      if (this.securityManager) {
        this.securityManager.cleanup();
      }
    }, 60 * 60 * 1000);
  }

  // 从存储中加载认证状态
  async loadAuthState() {
    try {
      const data = await chrome.storage.local.get(Object.values(this.STORAGE_KEYS));

      this.isAuthenticated = data[this.STORAGE_KEYS.LOGIN_KEY] || false;
      this.userInfo = data[this.STORAGE_KEYS.USER_SETTINGS] || null;
      this.organizations = data[this.STORAGE_KEYS.MY_ORGS_KEY] || [];
      this.selectedCorpId = data[this.STORAGE_KEYS.SELECTED_CORP_ID] || null;

      const authTimestamp = data[this.STORAGE_KEYS.AUTH_TIMESTAMP];
      if (authTimestamp) {
        // 检查认证是否过期（24小时）
        const now = Date.now();
        const authAge = now - authTimestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (authAge > maxAge) {
          console.log('认证状态已过期，清理本地状态');
          await this.clearAuthState();
        }
      }

    } catch (error) {
      console.error('加载认证状态失败:', error);
      await this.clearAuthState();
    }
  }

  // 保存认证状态到存储
  async saveAuthState() {
    try {
      const data = {
        [this.STORAGE_KEYS.LOGIN_KEY]: this.isAuthenticated,
        [this.STORAGE_KEYS.USER_SETTINGS]: this.userInfo,
        [this.STORAGE_KEYS.MY_ORGS_KEY]: this.organizations,
        [this.STORAGE_KEYS.SELECTED_CORP_ID]: this.selectedCorpId,
        [this.STORAGE_KEYS.AUTH_TIMESTAMP]: Date.now()
      };

      await chrome.storage.local.set(data);
      console.log('认证状态已保存');

      // 保存状态后通知监听器
      this.notifyAuthChange();

    } catch (error) {
      console.error('保存认证状态失败:', error);
    }
  }

  // 清理认证状态
  async clearAuthState() {
    this.isAuthenticated = false;
    this.userInfo = null;
    this.organizations = [];
    this.selectedCorpId = null;
    
    try {
      await chrome.storage.local.remove(Object.values(this.STORAGE_KEYS));
      console.log('认证状态已清理');
      
      // 通知监听器
      this.notifyAuthChange();
      
    } catch (error) {
      console.error('清理认证状态失败:', error);
    }
  }

  // 验证钉钉认证Cookie
  async validateAuthCookie() {
    const traceId = `cookie_validate_${Date.now()}`;

    try {
      console.log(`[${traceId}] 开始验证钉钉认证Cookie`);

      // 获取所有钉钉域名下的Cookie
      const allCookies = await chrome.cookies.getAll({
        domain: '.dingtalk.com'
      });

      console.log(`[${traceId}] 找到 ${allCookies.length} 个钉钉Cookie`);

      // 检查关键认证Cookie（扩展列表）
      const authCookieNames = [
        'account', 'login_aliyunid_ticket', '_tb_token_',
        'sessionid', 'sid', 'token', 'auth', 'login', 'user', 'session',
        'dingtalk_session', 'dt_session', 'dt_token', 'dt_auth'
      ];

      const authCookies = allCookies.filter(cookie =>
        (authCookieNames.includes(cookie.name) ||
         cookie.name.includes('login') ||
         cookie.name.includes('auth') ||
         cookie.name.includes('session') ||
         cookie.name.includes('token')) &&
        cookie.value
      );

      if (authCookies.length === 0) {
        console.log(`[${traceId}] 未找到有效的钉钉认证Cookie`);
        return false;
      }

      console.log(`[${traceId}] 找到 ${authCookies.length} 个认证Cookie:`,
        authCookies.map(c => ({ name: c.name, hasValue: !!c.value, length: c.value?.length }))
      );

      // 检查主要的account Cookie
      const accountCookie = authCookies.find(c => c.name === 'account');
      if (accountCookie) {
        // 检查Cookie是否过期
        if (accountCookie.expirationDate && accountCookie.expirationDate * 1000 < Date.now()) {
          console.warn(`[${traceId}] 钉钉认证Cookie已过期`);
          return false;
        }

        // 检查Cookie值是否有效
        if (!accountCookie.value || accountCookie.value.length < 10) {
          console.warn(`[${traceId}] 钉钉认证Cookie值无效`);
          return false;
        }
      }

      // 如果有任何认证Cookie存在且有效，认为用户已登录
      const hasValidCookies = authCookies.some(cookie =>
        cookie.value && cookie.value.length > 5
      );

      if (hasValidCookies) {
        console.log(`[${traceId}] 钉钉认证Cookie验证通过`);
        return true;
      } else {
        console.log(`[${traceId}] 钉钉认证Cookie无效`);
        return false;
      }

    } catch (error) {
      console.error(`[${traceId}] 验证钉钉认证Cookie失败:`, error);
      return false;
    }
  }

  // 设置Cookie监听器
  setupCookieListener() {
    if (this.cookieListenerSetup) {
      console.log('Cookie监听器已设置，跳过重复设置');
      return;
    }

    if (chrome.cookies && chrome.cookies.onChanged) {
      // 绑定处理函数到实例，确保this上下文正确
      this.cookieChangeHandler = (changeInfo) => {
        this.handleCookieChange(changeInfo);
      };

      chrome.cookies.onChanged.addListener(this.cookieChangeHandler);
      this.cookieListenerSetup = true;
      console.log('钉钉Cookie监听器已设置');

      // 设置监听器健康检查，每分钟验证一次
      setInterval(() => {
        this.verifyCookieListener();
      }, 60000);

    } else {
      console.warn('Cookie API不可用，无法设置监听器');
      // 如果Cookie API不可用，增加定期检查频率作为补偿
      this.setupEnhancedPeriodicCheck();
    }
  }

  // 验证Cookie监听器是否正常工作
  verifyCookieListener() {
    if (!this.cookieListenerSetup) {
      console.warn('检测到Cookie监听器未设置，尝试重新设置');
      this.setupCookieListener();
    }
  }

  // 增强的定期检查（当Cookie监听器不可用时）
  setupEnhancedPeriodicCheck() {
    console.log('启用增强定期检查模式（Cookie监听器不可用）');
    // 更频繁的检查间隔（10秒）
    setInterval(async () => {
      try {
        const traceId = `enhanced_periodic_${Date.now()}`;
        console.debug(`[${traceId}] 增强定期检查认证状态...`);

        const cookieValid = await this.validateAuthCookie();
        const currentAuthState = this.isAuthenticated;

        if (cookieValid && !currentAuthState) {
          console.log(`[${traceId}] 检测到Cookie有效但本地状态未认证，触发登录处理`);
          await this.handleUserLogin(traceId);
        } else if (!cookieValid && currentAuthState) {
          console.log(`[${traceId}] 检测到Cookie无效但本地状态已认证，触发登出处理`);
          await this.handleUserLogout(traceId);
        }

      } catch (error) {
        console.error('增强定期检查认证状态失败:', error);
      }
    }, 10000); // 10秒检查一次
  }

  // 设置定期检查机制（作为Cookie监听器的备用）
  setupPeriodicCheck() {
    // 每30秒检查一次认证状态变化
    setInterval(async () => {
      try {
        const traceId = `periodic_${Date.now()}`;
        console.debug(`[${traceId}] 定期检查认证状态...`);

        // 检查Cookie状态
        const cookieValid = await this.validateAuthCookie();
        const currentAuthState = this.isAuthenticated;

        // 如果Cookie有效但本地状态显示未认证，触发登录处理
        if (cookieValid && !currentAuthState) {
          console.log(`[${traceId}] 检测到Cookie有效但本地状态未认证，触发登录处理`);
          await this.handleUserLogin(traceId);
        }
        // 如果Cookie无效但本地状态显示已认证，触发登出处理
        else if (!cookieValid && currentAuthState) {
          console.log(`[${traceId}] 检测到Cookie无效但本地状态已认证，触发登出处理`);
          await this.handleUserLogout(traceId);
        }

      } catch (error) {
        console.error('定期检查认证状态失败:', error);
      }
    }, 30000); // 30秒检查一次

    console.log('定期认证状态检查机制已设置（30秒间隔）');
  }

  // 处理Cookie变化（按照文档要求实现）
  async handleCookieChange(changeInfo) {
    const { removed, cookie } = changeInfo;
    const { name, domain } = cookie;

    // 严格按照文档要求：只监听钉钉域名下的account cookie
    if (domain === ".dingtalk.com" && name === "account") {
      const traceId = `cookie_${Date.now()}`;

      console.log(`[${traceId}] 钉钉account Cookie变化:`, {
        removed: removed,
        domain: domain,
        hasValue: !!cookie.value,
        cookieLength: cookie.value ? cookie.value.length : 0
      });

      if (!removed) {
        // Cookie被设置或更新，用户登录成功
        console.log(`[${traceId}] 检测到account Cookie设置，触发登录处理`);

        // 延迟处理，确保Cookie完全设置
        setTimeout(async () => {
          await this.handleUserLogin(traceId);
        }, 1000);
      } else {
        // Cookie被删除，用户登出
        console.log(`[${traceId}] 检测到account Cookie删除，触发登出处理`);
        await this.handleUserLogout(traceId);
      }
    } else {
      // 记录其他Cookie变化（仅用于调试）
      console.debug(`Cookie变化 (非关键):`, {
        name: name,
        domain: domain,
        removed: removed
      });
    }
  }

  // 处理用户登录
  async handleUserLogin(traceId) {
    console.log(`[${traceId}] 检测到用户登录，开始获取用户信息`);

    try {
      // 延迟一下确保Cookie完全设置
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 先验证Cookie状态
      const cookieValid = await this.validateAuthCookie();
      if (!cookieValid) {
        console.warn(`[${traceId}] Cookie验证失败，可能登录未完成`);
        return;
      }

      console.log(`[${traceId}] Cookie验证通过，开始获取用户信息`);

      // 获取用户信息和组织数据
      await this.fetchUserInfoAndSync();

      console.log(`[${traceId}] 用户信息获取完成，认证状态:`, {
        isAuthenticated: this.isAuthenticated,
        userName: this.userInfo?.name,
        orgCount: this.organizations?.length
      });

    } catch (error) {
      console.error(`[${traceId}] 处理用户登录失败:`, error);
      // 登录处理失败时，不要清理状态，让用户手动重试
    }
  }

  // 处理用户登出
  async handleUserLogout(traceId) {
    console.log(`[${traceId}] 检测到用户登出，清理本地状态`);
    await this.clearAuthState();
  }

  // 获取用户信息并同步状态
  async fetchUserInfoAndSync() {
    const traceId = `fetch_${Date.now()}`;
    console.log(`[${traceId}] 开始获取用户信息和组织数据`);

    try {
      const baseUrl = this.getApiBaseUrl();
      let hasValidUserInfo = false;
      let hasValidOrgInfo = false;

      // 获取用户组织信息 - 尝试多个端点
      try {
        const orgsResponse = await this.tryMultipleOrgEndpoints(baseUrl, traceId);

        if (orgsResponse) {
          console.log(`[${traceId}] 组织信息原始响应:`, orgsResponse);

          // 兼容不同的响应格式
          let orgsData = null;
          if (orgsResponse && orgsResponse.success && orgsResponse.data) {
            orgsData = orgsResponse.data;
          } else if (orgsResponse && Array.isArray(orgsResponse)) {
            orgsData = orgsResponse;
          } else if (orgsResponse && orgsResponse.result) {
            orgsData = orgsResponse.result;
          } else if (orgsResponse && orgsResponse.data && Array.isArray(orgsResponse.data)) {
            orgsData = orgsResponse.data;
          }

          if (orgsData && Array.isArray(orgsData) && orgsData.length > 0) {
            this.organizations = orgsData.map(org => ({
              corpId: org.corpId || org.id || org.corpid,
              corpName: org.corpName || org.name || org.corpname || org.organizationName,
              permissions: org.permissions || [],
              isDefault: org.isDefault || org.default || false
            }));

            // 自动选择默认企业
            if (!this.selectedCorpId && this.organizations.length > 0) {
              const defaultOrg = this.organizations.find(org => org.isDefault) || this.organizations[0];
              this.selectedCorpId = defaultOrg.corpId;
            }

            hasValidOrgInfo = true;
            console.log(`[${traceId}] 组织信息获取成功，共 ${this.organizations.length} 个组织:`, this.organizations);
          } else {
            console.warn(`[${traceId}] 组织信息响应格式异常或为空:`, orgsResponse);
            this.organizations = [];
          }
        } else {
          console.warn(`[${traceId}] 所有组织信息端点都失败`);
          this.organizations = [];
        }
      } catch (orgError) {
        console.error(`[${traceId}] 获取组织信息过程异常:`, orgError);
        this.organizations = [];
      }

      // 获取用户设置 - 尝试多个端点
      try {
        const settingsResponse = await this.tryMultipleUserEndpoints(baseUrl, traceId);

        if (settingsResponse) {
          console.log(`[${traceId}] 用户设置原始响应:`, settingsResponse);

          // 兼容不同的响应格式
          let userData = null;
          if (settingsResponse && settingsResponse.success && settingsResponse.data) {
            userData = settingsResponse.data;
          } else if (settingsResponse && settingsResponse.result) {
            userData = settingsResponse.result;
          } else if (settingsResponse && settingsResponse.data) {
            userData = settingsResponse.data;
          } else if (settingsResponse && (settingsResponse.name || settingsResponse.userId || settingsResponse.id)) {
            userData = settingsResponse;
          }

          if (userData && (userData.name || userData.userId || userData.id || userData.displayName)) {
            this.userInfo = {
              name: userData.name || userData.displayName || userData.realName || userData.nickName || '钉钉用户',
              avatar: userData.avatar || userData.avatarUrl || userData.headUrl || '',
              userId: userData.userId || userData.id || userData.uid || '',
              email: userData.email || userData.emailAddress || userData.mail || ''
            };
            hasValidUserInfo = true;
            console.log(`[${traceId}] 用户信息获取成功:`, this.userInfo);
          } else {
            console.warn(`[${traceId}] 用户信息响应格式异常或为空:`, settingsResponse);
            this.userInfo = {
              name: '钉钉用户',
              avatar: '',
              userId: '',
              email: ''
            };
          }
        } else {
          console.warn(`[${traceId}] 所有用户信息端点都失败`);
          this.userInfo = {
            name: '钉钉用户',
            avatar: '',
            userId: '',
            email: ''
          };
        }
      } catch (userError) {
        console.error(`[${traceId}] 获取用户设置过程异常:`, userError);
        this.userInfo = {
          name: '钉钉用户',
          avatar: '',
          userId: '',
          email: ''
        };
      }

      // 更新认证状态 - 只有在获取到有效信息时才认为认证成功
      if (hasValidUserInfo || hasValidOrgInfo) {
        this.isAuthenticated = true;
        console.log(`[${traceId}] 认证状态更新为已认证 (用户信息: ${hasValidUserInfo}, 组织信息: ${hasValidOrgInfo})`);
      } else {
        this.isAuthenticated = false;
        console.warn(`[${traceId}] 未获取到有效的用户或组织信息，认证状态设为未认证`);
      }

      // 保存状态（会自动触发通知）
      await this.saveAuthState();

      console.log(`[${traceId}] 用户信息同步完成:`, {
        isAuthenticated: this.isAuthenticated,
        userInfo: this.userInfo,
        organizationCount: this.organizations.length,
        selectedCorpId: this.selectedCorpId,
        hasValidUserInfo,
        hasValidOrgInfo
      });

    } catch (error) {
      console.error(`[${traceId}] 获取用户信息过程中发生异常:`, error);

      // 检查Cookie是否仍然有效
      const cookieValid = await this.validateAuthCookie();

      if (cookieValid) {
        // Cookie有效但API调用失败，可能是网络问题或API端点问题
        console.warn(`[${traceId}] Cookie有效但API调用失败，保持认证状态但使用默认用户信息`);
        this.isAuthenticated = true;
        this.userInfo = {
          name: '钉钉用户',
          avatar: '',
          userId: '',
          email: ''
        };
        this.organizations = [];
      } else {
        // Cookie无效，清理认证状态
        console.warn(`[${traceId}] Cookie无效，清理认证状态`);
        this.isAuthenticated = false;
        this.userInfo = null;
        this.organizations = [];
        this.selectedCorpId = null;
      }

      // 保存状态（会自动触发通知）
      await this.saveAuthState();
    }
  }

  // 尝试多个API端点获取用户信息
  async tryMultipleUserEndpoints(baseUrl, traceId) {
    const userEndpoints = [
      '/openapi/api/user/settings',
      '/openapi/api/user/profile',
      '/api/user/info',
      '/portal/api/user/current',
      '/api/v1/user/me'
    ];

    console.log(`[${traceId}] 开始尝试 ${userEndpoints.length} 个用户信息端点`);

    for (const endpoint of userEndpoints) {
      try {
        console.log(`[${traceId}] 尝试用户信息端点: ${baseUrl}${endpoint}`);
        const response = await this.makeSecureRequest(`${baseUrl}${endpoint}`);

        console.log(`[${traceId}] 端点 ${endpoint} 原始响应:`, response);

        // 更详细的响应检查
        if (response) {
          const hasDirectUserInfo = response.name || response.userId || response.id || response.displayName;
          const hasDataField = response.data && (response.data.name || response.data.userId || response.data.id);
          const hasResultField = response.result && (response.result.name || response.result.userId || response.result.id);

          console.log(`[${traceId}] 端点 ${endpoint} 数据检查:`, {
            hasDirectUserInfo,
            hasDataField,
            hasResultField,
            responseKeys: Object.keys(response)
          });

          if (hasDirectUserInfo || hasDataField || hasResultField) {
            console.log(`[${traceId}] 用户信息端点成功: ${endpoint}`, response);
            return response;
          } else {
            console.warn(`[${traceId}] 端点 ${endpoint} 响应无有效用户信息:`, response);
          }
        } else {
          console.warn(`[${traceId}] 端点 ${endpoint} 响应为空`);
        }
      } catch (error) {
        console.warn(`[${traceId}] 用户信息端点失败 ${endpoint}:`, error.message);
        console.warn(`[${traceId}] 端点 ${endpoint} 错误详情:`, error);
      }
    }

    console.warn(`[${traceId}] 所有用户信息端点都失败或无有效数据`);
    return null;
  }

  // 尝试多个API端点获取组织信息
  async tryMultipleOrgEndpoints(baseUrl, traceId) {
    const orgEndpoints = [
      '/portal/api/v1/mine/orgs?orgTypes=1,2,3',
      '/api/v1/organizations',
      '/openapi/api/user/orgs',
      '/portal/api/orgs/mine',
      '/api/organizations/current'
    ];

    for (const endpoint of orgEndpoints) {
      try {
        console.log(`[${traceId}] 尝试组织信息端点: ${baseUrl}${endpoint}`);
        const response = await this.makeSecureRequest(`${baseUrl}${endpoint}`);

        if (response && (Array.isArray(response) || response.data || response.result)) {
          console.log(`[${traceId}] 组织信息端点成功: ${endpoint}`, response);
          return response;
        }
      } catch (error) {
        console.warn(`[${traceId}] 组织信息端点失败 ${endpoint}:`, error.message);
      }
    }

    return null;
  }

  // 安全的API请求
  async makeSecureRequest(url, options = {}) {
    const traceId = `api_${Date.now()}`;
    console.log(`[${traceId}] 发起API请求:`, url);

    // 验证URL是否为钉钉域名
    if (!this.isValidDingTalkUrl(url)) {
      const error = new Error('无效的钉钉API端点');
      console.error(`[${traceId}] URL验证失败:`, error);
      throw error;
    }

    const secureOptions = {
      method: 'GET',
      ...options,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, secureOptions);

      console.log(`[${traceId}] API响应状态:`, response.status, response.statusText);
      console.log(`[${traceId}] API响应头:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[${traceId}] API响应错误 (${response.status}):`, errorText);

        // 特殊处理一些常见的错误状态
        if (response.status === 401) {
          throw new Error(`认证失败: 请重新登录钉钉 (${response.status})`);
        } else if (response.status === 403) {
          throw new Error(`权限不足: 无法访问用户信息 (${response.status})`);
        } else if (response.status === 404) {
          throw new Error(`API端点不存在: ${url} (${response.status})`);
        } else {
          throw new Error(`钉钉API调用失败: ${response.status} ${response.statusText} - ${errorText}`);
        }
      }

      const contentType = response.headers.get('content-type');
      let responseData;

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        const textData = await response.text();
        console.warn(`[${traceId}] 非JSON响应 (${contentType}):`, textData.substring(0, 200) + (textData.length > 200 ? '...' : ''));
        try {
          responseData = JSON.parse(textData);
        } catch (e) {
          throw new Error(`响应不是有效的JSON格式: ${textData.substring(0, 100)}...`);
        }
      }

      console.log(`[${traceId}] API响应成功 (${Object.keys(responseData).length} 个字段):`, responseData);

      // 额外的响应数据分析
      if (responseData && typeof responseData === 'object') {
        console.log(`[${traceId}] 响应数据分析:`, {
          hasSuccess: 'success' in responseData,
          hasData: 'data' in responseData,
          hasResult: 'result' in responseData,
          hasError: 'error' in responseData,
          hasMessage: 'message' in responseData,
          topLevelKeys: Object.keys(responseData)
        });
      }

      return responseData;

    } catch (error) {
      console.error(`[${traceId}] API请求异常:`, error);
      console.error(`[${traceId}] 请求URL:`, url);
      console.error(`[${traceId}] 请求选项:`, secureOptions);
      throw error;
    }
  }

  // 验证钉钉URL
  isValidDingTalkUrl(url) {
    try {
      const urlObj = new URL(url);

      // 检查协议必须是HTTPS
      if (urlObj.protocol !== 'https:') {
        console.error('钉钉API URL必须使用HTTPS协议:', url);
        return false;
      }

      // 检查域名必须是钉钉官方域名
      const validDomains = [
        'docs.dingtalk.com',
        'pre-docs.dingtalk.com',
        'oapi.dingtalk.com',
        'api.dingtalk.com'
      ];

      const isValidDomain = validDomains.some(domain =>
        urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      );

      if (!isValidDomain) {
        console.error('无效的钉钉域名:', urlObj.hostname);
        return false;
      }

      // 检查路径是否包含可疑字符
      if (urlObj.pathname.includes('..') || urlObj.pathname.includes('<') || urlObj.pathname.includes('>')) {
        console.error('钉钉API URL路径包含可疑字符:', urlObj.pathname);
        return false;
      }

      return true;

    } catch (error) {
      console.error('URL解析失败:', error);
      return false;
    }
  }

  // 发起登录流程
  async initiateLogin() {
    try {
      const authUrl = this.getAuthUrl();
      console.log('打开钉钉登录页面:', authUrl);
      
      const tab = await chrome.tabs.create({
        url: authUrl,
        active: true
      });
      
      console.log('钉钉登录页面已打开，标签页ID:', tab.id);
      return { success: true, tabId: tab.id };
      
    } catch (error) {
      console.error('打开钉钉登录页面失败:', error);
      throw error;
    }
  }

  // 选择组织
  async selectOrganization(corpId) {
    const org = this.organizations.find(o => o.corpId === corpId);
    if (!org) {
      throw new Error('无效的组织ID');
    }

    this.selectedCorpId = corpId;
    // 保存状态（会自动触发通知）
    await this.saveAuthState();

    console.log('已选择组织:', org.corpName);

    return org;
  }

  // 登出
  async logout() {
    try {
      // 清理钉钉域名下的所有Cookie
      const cookies = await chrome.cookies.getAll({
        domain: '.dingtalk.com'
      });
      
      for (const cookie of cookies) {
        await chrome.cookies.remove({
          url: `https://${cookie.domain}${cookie.path}`,
          name: cookie.name
        });
      }
      
      // 清理本地状态
      await this.clearAuthState();
      
      console.log('用户已登出');
      return { success: true };
      
    } catch (error) {
      console.error('登出失败:', error);
      throw error;
    }
  }

  // 添加认证状态监听器
  addAuthListener(listener) {
    this.authListeners.add(listener);
  }

  // 移除认证状态监听器
  removeAuthListener(listener) {
    this.authListeners.delete(listener);
  }

  // 通知认证状态变化
  notifyAuthChange() {
    const authData = {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      organizations: this.organizations,
      selectedCorpId: this.selectedCorpId,
      selectedOrganization: this.organizations ?
        this.organizations.find(org => org.corpId === this.selectedCorpId) : null,
      timestamp: Date.now()
    };

    // 通知内存中的监听器
    this.authListeners.forEach(listener => {
      try {
        listener(authData);
      } catch (error) {
        console.error('认证状态监听器执行失败:', error);
      }
    });

    // 广播消息到所有UI组件
    this.broadcastAuthChange(authData);
  }

  // 广播认证状态变化到所有UI组件
  async broadcastAuthChange(authData) {
    const traceId = `broadcast_${Date.now()}`;
    console.log(`[${traceId}] 广播认证状态变化:`, {
      isAuthenticated: authData.isAuthenticated,
      hasUserInfo: !!authData.userInfo,
      orgCount: authData.organizations?.length || 0
    });

    try {
      // 发送消息到所有扩展页面（sidebar、options等）
      const message = {
        type: 'DINGTALK_AUTH_STATUS_CHANGED',
        data: authData,
        traceId: traceId
      };

      // 使用chrome.runtime.sendMessage发送到扩展内部页面
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        try {
          chrome.runtime.sendMessage(message);
          console.log(`[${traceId}] 已发送认证状态变化消息到扩展页面`);
        } catch (error) {
          console.warn(`[${traceId}] 发送消息到扩展页面失败:`, error);
        }
      }

      // 发送消息到所有标签页（如果有content script）
      if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.query) {
        try {
          const tabs = await chrome.tabs.query({});
          for (const tab of tabs) {
            try {
              await chrome.tabs.sendMessage(tab.id, message);
            } catch (error) {
              // 忽略无法发送消息的标签页（如chrome://页面）
              console.debug(`[${traceId}] 无法向标签页 ${tab.id} 发送消息`);
            }
          }
          console.log(`[${traceId}] 已发送认证状态变化消息到 ${tabs.length} 个标签页`);
        } catch (error) {
          console.warn(`[${traceId}] 发送消息到标签页失败:`, error);
        }
      }

    } catch (error) {
      console.error(`[${traceId}] 广播认证状态变化失败:`, error);
    }
  }

  // 获取当前认证状态
  getAuthStatus() {
    // 进行状态完整性检查
    const status = {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      organizations: this.organizations || [],
      selectedCorpId: this.selectedCorpId,
      selectedOrganization: this.organizations ?
        this.organizations.find(org => org.corpId === this.selectedCorpId) : null,
      environment: this.environment,
      lastUpdateTime: Date.now()
    };

    // 验证状态一致性
    if (status.isAuthenticated) {
      // 如果声称已认证，但没有用户信息，标记为异常
      if (!status.userInfo || !status.userInfo.name) {
        console.warn('认证状态异常：已认证但缺少用户信息');
        status.statusWarning = '认证状态异常，建议重新登录';
      }

      // 如果有选中的组织ID但找不到对应组织，标记为异常
      if (status.selectedCorpId && !status.selectedOrganization) {
        console.warn('组织状态异常：选中的组织不存在');
        status.statusWarning = '组织状态异常，请重新选择组织';
      }
    }

    return status;
  }

  // 验证认证状态完整性
  async validateAuthIntegrity() {
    const traceId = `integrity_${Date.now()}`;
    console.log(`[${traceId}] 开始验证认证状态完整性`);

    try {
      // 检查Cookie状态
      const cookieValid = await this.validateAuthCookie();

      // 检查本地状态
      const localStateValid = this.isAuthenticated && this.userInfo && this.userInfo.name;

      // 状态一致性检查
      if (cookieValid && !localStateValid) {
        console.warn(`[${traceId}] Cookie有效但本地状态无效，尝试恢复`);
        await this.fetchUserInfoAndSync();
        return true;
      }

      if (!cookieValid && localStateValid) {
        console.warn(`[${traceId}] Cookie无效但本地状态有效，清理本地状态`);
        await this.clearAuthState();
        return false;
      }

      if (!cookieValid && !localStateValid) {
        console.log(`[${traceId}] Cookie和本地状态都无效，状态一致`);
        return false;
      }

      console.log(`[${traceId}] 认证状态完整性验证通过`);
      return true;

    } catch (error) {
      console.error(`[${traceId}] 认证状态完整性验证失败:`, error);
      return false;
    }
  }

  // 设置增强的状态同步机制
  setupEnhancedStateSync() {
    // 监听存储变化，确保多个实例之间的状态同步
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
          // 检查是否有认证相关的存储变化
          const authKeys = Object.values(this.STORAGE_KEYS);
          const hasAuthChanges = Object.keys(changes).some(key => authKeys.includes(key));

          if (hasAuthChanges) {
            console.log('检测到认证状态存储变化，同步本地状态');
            // 延迟同步，避免频繁更新
            setTimeout(() => {
              this.syncStateFromStorage();
            }, 500);
          }
        }
      });
    }

    // 设置页面可见性变化监听（当页面重新激活时检查状态）
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          console.log('页面重新激活，检查认证状态');
          setTimeout(() => {
            this.validateAndSyncAuthState();
          }, 1000);
        }
      });
    }
  }

  // 从存储同步状态（不触发通知，避免循环）
  async syncStateFromStorage() {
    try {
      const data = await chrome.storage.local.get(Object.values(this.STORAGE_KEYS));

      const newIsAuthenticated = data[this.STORAGE_KEYS.LOGIN_KEY] || false;
      const newUserInfo = data[this.STORAGE_KEYS.USER_SETTINGS] || null;
      const newOrganizations = data[this.STORAGE_KEYS.MY_ORGS_KEY] || [];
      const newSelectedCorpId = data[this.STORAGE_KEYS.SELECTED_CORP_ID] || null;

      // 只有状态真正发生变化时才更新
      let hasChanges = false;

      if (this.isAuthenticated !== newIsAuthenticated) {
        this.isAuthenticated = newIsAuthenticated;
        hasChanges = true;
      }

      if (JSON.stringify(this.userInfo) !== JSON.stringify(newUserInfo)) {
        this.userInfo = newUserInfo;
        hasChanges = true;
      }

      if (JSON.stringify(this.organizations) !== JSON.stringify(newOrganizations)) {
        this.organizations = newOrganizations;
        hasChanges = true;
      }

      if (this.selectedCorpId !== newSelectedCorpId) {
        this.selectedCorpId = newSelectedCorpId;
        hasChanges = true;
      }

      if (hasChanges) {
        console.log('状态已从存储同步');
        this.notifyAuthChange();
      }

    } catch (error) {
      console.error('从存储同步状态失败:', error);
    }
  }

  // 验证并同步认证状态
  async validateAndSyncAuthState() {
    try {
      const cookieValid = await this.validateAuthCookie();
      const localStateValid = this.isAuthenticated;

      if (cookieValid && !localStateValid) {
        console.log('检测到Cookie有效但本地状态未认证，触发状态同步');
        await this.handleUserLogin(`sync_${Date.now()}`);
      } else if (!cookieValid && localStateValid) {
        console.log('检测到Cookie无效但本地状态已认证，清理状态');
        await this.handleUserLogout(`sync_${Date.now()}`);
      }
    } catch (error) {
      console.error('验证并同步认证状态失败:', error);
    }
  }

  // 测试所有API端点
  async testAllApiEndpoints() {
    const traceId = `test_api_${Date.now()}`;
    console.log(`[${traceId}] 开始测试所有API端点`);

    const baseUrl = this.getApiBaseUrl();
    const results = {
      baseUrl,
      userEndpoints: [],
      orgEndpoints: [],
      summary: {
        totalTested: 0,
        successful: 0,
        failed: 0,
        hasUserData: 0,
        hasOrgData: 0
      }
    };

    // 测试用户信息端点
    const userEndpoints = [
      '/openapi/api/user/settings',
      '/openapi/api/user/profile',
      '/api/user/info',
      '/portal/api/user/current',
      '/api/v1/user/me'
    ];

    for (const endpoint of userEndpoints) {
      const testResult = {
        endpoint,
        url: `${baseUrl}${endpoint}`,
        success: false,
        error: null,
        response: null,
        hasUserData: false,
        userDataFields: []
      };

      try {
        console.log(`[${traceId}] 测试用户端点: ${endpoint}`);
        const response = await this.makeSecureRequest(`${baseUrl}${endpoint}`);

        testResult.success = true;
        testResult.response = response;

        // 检查是否包含用户数据
        const userData = this.extractUserDataFromResponse(response);
        if (userData) {
          testResult.hasUserData = true;
          testResult.userDataFields = Object.keys(userData).filter(key => userData[key]);
          results.summary.hasUserData++;
        }

        results.summary.successful++;
      } catch (error) {
        testResult.error = error.message;
        results.summary.failed++;
      }

      results.userEndpoints.push(testResult);
      results.summary.totalTested++;
    }

    console.log(`[${traceId}] API端点测试完成:`, results.summary);
    return results;
  }

  // 从响应中提取用户数据
  extractUserDataFromResponse(response) {
    if (!response) return null;

    // 尝试不同的响应格式
    let userData = null;
    if (response.success && response.data) {
      userData = response.data;
    } else if (response.result) {
      userData = response.result;
    } else if (response.data) {
      userData = response.data;
    } else if (response.name || response.userId || response.id) {
      userData = response;
    }

    return userData;
  }

  // 调试方法：获取详细的认证状态信息
  async getDebugInfo() {
    const traceId = `debug_${Date.now()}`;
    console.log(`[${traceId}] 获取钉钉认证调试信息`);

    try {
      // 获取Cookie信息
      const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
      const accountCookie = cookies.find(c => c.name === 'account');

      // 获取存储信息
      const storageData = await chrome.storage.local.get(Object.values(this.STORAGE_KEYS));

      // 获取监听器状态
      const listenerStatus = {
        cookieListenerSetup: this.cookieListenerSetup,
        authListenersCount: this.authListeners.size
      };

      const debugInfo = {
        traceId: traceId,
        timestamp: new Date().toISOString(),

        // 内存状态
        memoryState: {
          isAuthenticated: this.isAuthenticated,
          hasUserInfo: !!this.userInfo,
          userInfoName: this.userInfo?.name,
          organizationCount: this.organizations?.length || 0,
          selectedCorpId: this.selectedCorpId,
          environment: this.environment
        },

        // Cookie状态
        cookieState: {
          hasCookies: cookies.length > 0,
          hasAccountCookie: !!accountCookie,
          accountCookieValue: accountCookie ? '***存在***' : '不存在',
          accountCookieExpiry: accountCookie?.expirationDate,
          totalCookieCount: cookies.length
        },

        // 存储状态
        storageState: {
          loginKey: storageData[this.STORAGE_KEYS.LOGIN_KEY],
          hasUserSettings: !!storageData[this.STORAGE_KEYS.USER_SETTINGS],
          hasOrganizations: !!storageData[this.STORAGE_KEYS.MY_ORGS_KEY],
          selectedCorpId: storageData[this.STORAGE_KEYS.SELECTED_CORP_ID],
          authTimestamp: storageData[this.STORAGE_KEYS.AUTH_TIMESTAMP]
        },

        // 监听器状态
        listenerStatus: listenerStatus,

        // 状态一致性检查
        consistency: {
          memoryStorageMatch: this.isAuthenticated === (storageData[this.STORAGE_KEYS.LOGIN_KEY] || false),
          cookieMemoryMatch: !!accountCookie === this.isAuthenticated
        }
      };

      console.log(`[${traceId}] 钉钉认证调试信息:`, debugInfo);
      return debugInfo;

    } catch (error) {
      console.error(`[${traceId}] 获取调试信息失败:`, error);
      return {
        traceId: traceId,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// 导出单例实例
if (typeof window !== 'undefined') {
  window.DingTalkAuthManager = DingTalkAuthManager;
}
